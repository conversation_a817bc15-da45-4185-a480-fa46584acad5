import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:tpa_log_reader/app.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // 禁用调试模式下的渲染溢出警告（红色竖直文字）
  if (kDebugMode) {
    // 禁用渲染溢出警告
    FlutterError.onError = (FlutterErrorDetails details) {
      // 过滤掉渲染溢出错误，但保留其他错误
      if (!details.toString().contains('RenderFlex overflowed')) {
        FlutterError.presentError(details);
      }
    };
  }

  runApp(const App());
}